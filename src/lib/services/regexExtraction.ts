import type { ExtractedJobData } from './aiExtraction';

export interface RegexExtractionResult {
  success: boolean;
  data?: ExtractedJobData;
  error?: string;
}

export class RegexExtractionService {
  
  extractJobData(jobContent: string): RegexExtractionResult {
    try {
      const data: ExtractedJobData = {
        title: '',
        description: jobContent.trim()
      };

      // Extract title (look for common patterns in English and Swedish)
      data.title = this.extractTitle(jobContent);
      
      // Extract dates
      data.startDate = this.extractStartDate(jobContent);
      data.endDate = this.extractEndDate(jobContent);
      
      // Extract location
      data.location = this.extractLocation(jobContent);
      
      // Extract rate
      data.rate = this.extractRate(jobContent);

      return {
        success: true,
        data
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Regex extraction failed'
      };
    }
  }

  private extractTitle(jobContent: string): string {
    const titlePatterns = [
      // High priority: First line patterns (often contains the job title)
      /^([^\n\r]{10,100})/m, // First substantial line (10-100 chars)

      // Swedish "söker" patterns (very common in Swedish job ads)
      /söker\s+(?:nu\s+)?(?:en\s+)?([^\n\r.!?]+)/i,
      /vi\s+söker\s+(?:en\s+)?([^\n\r.!?]+)/i,
      /företaget\s+söker\s+(?:en\s+)?([^\n\r.!?]+)/i,

      // Specific field labels
      /titel[:\s]+([^\n\r]+)/i,
      /job title[:\s]+([^\n\r]+)/i,
      /position[:\s]+([^\n\r]+)/i,
      /befattning[:\s]+([^\n\r]+)/i,
      /tjänst[:\s]+([^\n\r]+)/i,
      /uppdrag[:\s]+([^\n\r]+)/i,
      /role[:\s]+([^\n\r]+)/i,

      // Role-specific patterns (Swedish)
      /^([^\n\r]*utvecklare[^\n\r]*)/im,
      /^([^\n\r]*ingenjör[^\n\r]*)/im,
      /^([^\n\r]*konsult[^\n\r]*)/im,
      /^([^\n\r]*chef[^\n\r]*)/im,
      /^([^\n\r]*specialist[^\n\r]*)/im,
      /^([^\n\r]*analytiker[^\n\r]*)/im,
      /^([^\n\r]*projektledare[^\n\r]*)/im,

      // Role-specific patterns (English)
      /^([^\n\r]*developer[^\n\r]*)/im,
      /^([^\n\r]*engineer[^\n\r]*)/im,
      /^([^\n\r]*manager[^\n\r]*)/im,
      /^([^\n\r]*analyst[^\n\r]*)/im,
      /^([^\n\r]*consultant[^\n\r]*)/im
    ];
    
    for (const pattern of titlePatterns) {
      const match = jobContent.match(pattern);
      if (match && match[1]) {
        let extractedTitle = match[1].trim();

        // Clean up the extracted title
        extractedTitle = extractedTitle
          .replace(/^[^\w\s]*/, '') // Remove leading non-word characters
          .replace(/[^\w\s]*$/, '') // Remove trailing non-word characters
          .replace(/\s+/g, ' ') // Normalize whitespace
          .replace(/^(vi\s+)?söker\s+(nu\s+)?(en\s+)?/i, '') // Remove "vi söker nu en" prefix
          .replace(/^(the\s+)?(a\s+)?/i, '') // Remove "the" or "a" prefix
          .trim();

        // Validate the extracted title
        if (extractedTitle.length >= 5 && extractedTitle.length <= 150 &&
          !/^(detaljer|referens|ort|plats|startdatum|slutdatum|omfattning|distansarbete)/i.test(extractedTitle) &&
          !/^(details|reference|location|start|end|scope|remote)/i.test(extractedTitle)) {
          return `DocUploadTest: ${extractedTitle}`;
        }
      }
    }
    
    return '';
  }

  private extractStartDate(jobContent: string): string | undefined {
    const datePatterns = [
      // English patterns
      /start date[:\s]+([^\n]+)/i,
      /starting[:\s]+([^\n]+)/i,
      /from[:\s]+([^\n]+)/i,
      /begins?[:\s]+([^\n]+)/i,

      // Swedish patterns
      /startdatum[:\s]+([^\n]+)/i,
      /start[:\s]+([^\n]+)/i,
      /börjar[:\s]+([^\n]+)/i,
      /från[:\s]+([^\n]+)/i,
      /påbörjas[:\s]+([^\n]+)/i
    ];

    for (const pattern of datePatterns) {
      const match = jobContent.match(pattern);
      if (match && match[1]) {
        const extracted = this.extractDate(match[1]);
        if (extracted) return extracted;
      }
    }
    
    return undefined;
  }

  private extractEndDate(jobContent: string): string | undefined {
    const endDatePatterns = [
      // English patterns
      /end date[:\s]+([^\n]+)/i,
      /until[:\s]+([^\n]+)/i,
      /to[:\s]+([^\n]+)/i,
      /ends?[:\s]+([^\n]+)/i,

      // Swedish patterns
      /slutdatum[:\s]+([^\n]+)/i,
      /slut[:\s]+([^\n]+)/i,
      /till[:\s]+([^\n]+)/i,
      /avslutas[:\s]+([^\n]+)/i,
      /varaktighet[:\s]+([^\n]+)/i
    ];

    for (const pattern of endDatePatterns) {
      const match = jobContent.match(pattern);
      if (match && match[1]) {
        const extracted = this.extractDate(match[1]);
        if (extracted) return extracted;
      }
    }
    
    return undefined;
  }

  private extractLocation(jobContent: string): string | undefined {
    const locationPatterns = [
      // High priority Swedish patterns (most specific first)
      /ort\s*:\s*([^\n\r]+)/i,
      /plats\s*:\s*([^\n\r]+)/i,
      /stad\s*:\s*([^\n\r]+)/i,
      /arbetsplats\s*:\s*([^\n\r]+)/i,
      /kontor\s*:\s*([^\n\r]+)/i,

      // High priority English patterns
      /location\s*:\s*([^\n\r]+)/i,
      /city\s*:\s*([^\n\r]+)/i,
      /office\s*:\s*([^\n\r]+)/i,

      // Lower priority patterns (more general)
      /based in[:\s]+([^\n\r]+)/i,
      /workplace[:\s]+([^\n\r]+)/i,
      /belägen[:\s]+([^\n\r]+)/i,
      /placering[:\s]+([^\n\r]+)/i,

      // Fallback patterns
      /plats[:\s]+([^\n\r]+)/i,
      /location[:\s]+([^\n\r]+)/i
    ];

    for (const pattern of locationPatterns) {
      const match = jobContent.match(pattern);
      if (match && match[1]) {
        let extractedLocation = match[1].trim();

        // Clean up the extracted location
        extractedLocation = extractedLocation
          .replace(/[,;].*$/, '') // Remove everything after comma or semicolon
          .replace(/\s+/g, ' ') // Normalize whitespace
          .trim();

        // Only use if it looks like a valid location (not too long, no weird characters)
        if (extractedLocation.length > 0 && extractedLocation.length < 50 &&
          !/[{}[\]()]/g.test(extractedLocation)) {
          return extractedLocation;
        }
      }
    }
    
    return undefined;
  }

  private extractRate(jobContent: string): string | undefined {
    const ratePatterns = [
      // English patterns
      /rate[:\s]+([^\n]+)/i,
      /salary[:\s]+([^\n]+)/i,
      /compensation[:\s]+([^\n]+)/i,
      /hourly[:\s]+([^\n]+)/i,
      /pay[:\s]+([^\n]+)/i,
      /wage[:\s]+([^\n]+)/i,

      // Swedish patterns
      /arvode[:\s]+([^\n]+)/i,
      /lön[:\s]+([^\n]+)/i,
      /ersättning[:\s]+([^\n]+)/i,
      /timersättning[:\s]+([^\n]+)/i,
      /timlön[:\s]+([^\n]+)/i,
      /pris[:\s]+([^\n]+)/i,
      /kostnad[:\s]+([^\n]+)/i,

      // Currency patterns (works for both languages)
      /(\d+\s*SEK[^\n]*)/i,
      /(\d+\s*kr[^\n]*)/i,
      /(\d+\s*kronor[^\n]*)/i,
      /(\d+\s*EUR[^\n]*)/i,
      /(\d+\s*USD[^\n]*)/i,
      /(\d+\s*[^\n]*tim[^\n]*)/i, // "per timme" or "per hour"
      /(\d+\s*[^\n]*månad[^\n]*)/i // "per månad" or "per month"
    ];

    for (const pattern of ratePatterns) {
      const match = jobContent.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }
    
    return undefined;
  }

  private extractDate(dateStr: string): string {
    // Enhanced date extraction for both English and Swedish formats
    let cleaned = dateStr.trim();

    // Handle Swedish month names
    const swedishMonths: { [key: string]: string } = {
      'januari': '01', 'jan': '01',
      'februari': '02', 'feb': '02',
      'mars': '03', 'mar': '03',
      'april': '04', 'apr': '04',
      'maj': '05',
      'juni': '06', 'jun': '06',
      'juli': '07', 'jul': '07',
      'augusti': '08', 'aug': '08',
      'september': '09', 'sep': '09',
      'oktober': '10', 'okt': '10',
      'november': '11', 'nov': '11',
      'december': '12', 'dec': '12'
    };

    // Replace Swedish month names with numbers
    for (const [swedish, number] of Object.entries(swedishMonths)) {
      const regex = new RegExp(swedish, 'gi');
      cleaned = cleaned.replace(regex, number);
    }

    // Handle various date formats
    // YYYY-MM-DD, DD/MM/YYYY, DD.MM.YYYY, DD-MM-YYYY
    const datePatterns = [
      /(\d{4})-(\d{1,2})-(\d{1,2})/,  // YYYY-MM-DD
      /(\d{1,2})[\/\.\-](\d{1,2})[\/\.\-](\d{4})/, // DD/MM/YYYY
      /(\d{1,2})\s+(\d{1,2})\s+(\d{4})/ // DD MM YYYY
    ];

    for (const pattern of datePatterns) {
      const match = cleaned.match(pattern);
      if (match) {
        let year, month, day;

        if (pattern === datePatterns[0]) {
          // YYYY-MM-DD format
          [, year, month, day] = match;
        } else {
          // DD/MM/YYYY or DD MM YYYY format
          [, day, month, year] = match;
        }

        // Ensure two-digit month and day
        month = month.padStart(2, '0');
        day = day.padStart(2, '0');

        const dateString = `${year}-${month}-${day}`;
        const date = new Date(dateString);

        if (!isNaN(date.getTime())) {
          return dateString;
        }
      }
    }

    // Fallback: try to parse as-is
    const fallbackCleaned = cleaned.replace(/[^\d\/\-\.]/g, '');
    const date = new Date(fallbackCleaned);
    if (!isNaN(date.getTime())) {
      return date.toISOString().split('T')[0];
    }

    return '';
  }
}

// Factory function to create service instance
export function createRegexExtractionService(): RegexExtractionService {
  return new RegexExtractionService();
}
