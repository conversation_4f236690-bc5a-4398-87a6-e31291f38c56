import OpenAI from 'openai';
import type { JobData } from '$lib/types/cinode';

export interface ExtractedJobData {
  title: string;
  description: string;
  startDate?: string;
  endDate?: string;
  location?: string;
  rate?: string;
  company?: string;
}

export interface AIExtractionResult {
  success: boolean;
  data?: ExtractedJobData;
  error?: string;
  confidence?: number;
}

export class AIExtractionService {
  private openai: OpenAI;

  constructor(apiKey: string) {
    this.openai = new OpenAI({
      apiKey: apiKey,
    });
  }

  async extractJobData(documentContent: string): Promise<AIExtractionResult> {
    try {
      const prompt = this.buildExtractionPrompt(documentContent);
      
      const completion = await this.openai.chat.completions.create({
        model: "gpt-4o-mini", // Using the more cost-effective model for testing
        messages: [
          {
            role: "system",
            content: "You are an expert at extracting structured job information from documents. You understand both English and Swedish job descriptions. Always respond with valid JSON."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.1, // Low temperature for consistent extraction
        max_tokens: 1000,
        response_format: { type: "json_object" }
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        return {
          success: false,
          error: "No response from AI model"
        };
      }

      const extractedData = JSON.parse(response);
      
      // Validate and clean the extracted data
      const cleanedData = this.validateAndCleanData(extractedData);
      
      return {
        success: true,
        data: cleanedData,
        confidence: extractedData.confidence || 0.8
      };

    } catch (error) {
      console.error('AI extraction error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown AI extraction error'
      };
    }
  }

  private buildExtractionPrompt(content: string): string {
    return `
Extract job information from the following document content. The document may be in English or Swedish.

Document content:
"""
${content}
"""

Please extract the following information and return it as a JSON object:

{
  "title": "Job title or position name",
  "description": "Full job description (keep original text, just clean up formatting)",
  "startDate": "Start date in YYYY-MM-DD format (if found)",
  "endDate": "End date in YYYY-MM-DD format (if found)",
  "location": "Work location/city (if found)",
  "rate": "Salary/rate information (if found)",
  "company": "Company name (if found)",
  "confidence": 0.9
}

Instructions:
1. For title: Look for job titles, positions, or roles. Common Swedish patterns include "söker", "vi söker en", etc.
2. For dates: Convert any date format to YYYY-MM-DD. Handle Swedish months (januari, februari, etc.)
3. For location: Look for city names, office locations, or work places
4. For rate: Include salary, hourly rates, or compensation information
5. For company: Extract company or client name if mentioned
6. Set confidence between 0.0-1.0 based on how clear the information is
7. If information is not found, use null for that field
8. Keep the description as the full original text, just clean up excessive whitespace

Return only valid JSON, no additional text.
`;
  }

  private validateAndCleanData(data: any): ExtractedJobData {
    const cleaned: ExtractedJobData = {
      title: this.cleanString(data.title) || '',
      description: this.cleanString(data.description) || '',
    };

    // Add optional fields if they exist and are valid
    if (data.startDate && this.isValidDate(data.startDate)) {
      cleaned.startDate = data.startDate;
    }

    if (data.endDate && this.isValidDate(data.endDate)) {
      cleaned.endDate = data.endDate;
    }

    if (data.location && this.cleanString(data.location)) {
      cleaned.location = this.cleanString(data.location);
    }

    if (data.rate && this.cleanString(data.rate)) {
      cleaned.rate = this.cleanString(data.rate);
    }

    if (data.company && this.cleanString(data.company)) {
      cleaned.company = this.cleanString(data.company);
    }

    return cleaned;
  }

  private cleanString(str: any): string | undefined {
    if (typeof str !== 'string') return undefined;
    
    const cleaned = str.trim().replace(/\s+/g, ' ');
    return cleaned.length > 0 ? cleaned : undefined;
  }

  private isValidDate(dateStr: string): boolean {
    if (!/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) return false;
    
    const date = new Date(dateStr);
    return !isNaN(date.getTime()) && date.toISOString().startsWith(dateStr);
  }
}

// Factory function to create service instance
export function createAIExtractionService(apiKey: string): AIExtractionService {
  return new AIExtractionService(apiKey);
}
