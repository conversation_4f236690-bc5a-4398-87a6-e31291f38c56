<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import type { CinodeCustomer, CinodeCustomersResponse } from '$lib/types/cinode';

	let jobContent = '';
	let fileName = '';

	// Form fields for extracted data
	let title = '';
	let description = '';
	let startDate = '';
	let endDate = '';
	let location = '';
	let company = '';
	let selectedCustomerId: number | null = null;
	let rate = '';

	// Customer data
	let customers: CinodeCustomer[] = [];
	let isLoadingCustomers = false;
	let customerError = '';

	let isExtracting = false;
	let isUploading = false;
	let extractionMethod = '';
	let extractionConfidence = 0;
	
	onMount(async () => {
		// Get content from previous page
		jobContent = sessionStorage.getItem('jobContent') || '';
		fileName = sessionStorage.getItem('uploadedFileName') || '';

		if (!jobContent) {
			goto('/upload');
			return;
		}

		// Fetch customers and auto-extract information in parallel
		await Promise.all([
			fetchCustomers(),
			extractInformation()
		]);
	});

	async function fetchCustomers() {
		isLoadingCustomers = true;
		customerError = '';

		try {
			const response = await fetch('/api/cinode/customers');
			const data: CinodeCustomersResponse = await response.json();

			if (response.ok && data.success) {
				customers = data.customers;
			} else {
				customerError = data.error || 'Failed to fetch customers';
				console.error('Failed to fetch customers:', data);
			}
		} catch (error) {
			customerError = 'Failed to fetch customers';
			console.error('Error fetching customers:', error);
		} finally {
			isLoadingCustomers = false;
		}
	}

	function handleCustomerChange() {
		// Update the company field with the selected customer's name
		if (selectedCustomerId) {
			const selectedCustomer = customers.find(c => c.id === selectedCustomerId);
			if (selectedCustomer) {
				company = selectedCustomer.name;
			}
		} else {
			company = '';
		}
	}
	
	async function extractInformation() {
		isExtracting = true;

		try {
			// First try AI extraction
			const aiResult = await tryAIExtraction();

			if (aiResult.success && aiResult.data) {
				// Use AI extracted data
				title = aiResult.data.title || '';
				startDate = aiResult.data.startDate || '';
				endDate = aiResult.data.endDate || '';
				location = aiResult.data.location || '';
				rate = aiResult.data.rate || '';
				company = aiResult.data.company || '';
				description = aiResult.data.description || jobContent.trim();

				extractionMethod = 'AI (GPT-4)';
				extractionConfidence = aiResult.confidence || 0.8;

				console.log('✅ AI extraction successful');
			} else {
				// Fallback to regex extraction
				console.log('⚠️ AI extraction failed, falling back to regex');
				await fallbackToRegexExtraction();
			}
		} catch (error) {
			console.error('Extraction error:', error);
			// Fallback to regex extraction
			await fallbackToRegexExtraction();
		}

		isExtracting = false;
	}

	async function tryAIExtraction() {
		try {
			const response = await fetch('/api/extract/ai', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ content: jobContent })
			});

			const result = await response.json();
			return result;
		} catch (error) {
			console.error('AI extraction API error:', error);
			return { success: false, error: 'API call failed' };
		}
	}

	async function fallbackToRegexExtraction() {
		// Use the original regex-based extraction as fallback
		extractionMethod = 'Regex (Fallback)';
		extractionConfidence = 0.6;

		// Extract title (look for common patterns in English and Swedish)
		const titlePatterns = [
			// High priority: First line patterns (often contains the job title)
			/^([^\n\r]{10,100})/m, // First substantial line (10-100 chars)

			// Swedish "söker" patterns (very common in Swedish job ads)
			/söker\s+(?:nu\s+)?(?:en\s+)?([^\n\r.!?]+)/i,
			/vi\s+söker\s+(?:en\s+)?([^\n\r.!?]+)/i,
			/företaget\s+söker\s+(?:en\s+)?([^\n\r.!?]+)/i,

			// Specific field labels
			/titel[:\s]+([^\n\r]+)/i,
			/job title[:\s]+([^\n\r]+)/i,
			/position[:\s]+([^\n\r]+)/i,
			/befattning[:\s]+([^\n\r]+)/i,
			/tjänst[:\s]+([^\n\r]+)/i,
			/uppdrag[:\s]+([^\n\r]+)/i,
			/role[:\s]+([^\n\r]+)/i,

			// Role-specific patterns (Swedish)
			/^([^\n\r]*utvecklare[^\n\r]*)/im,
			/^([^\n\r]*ingenjör[^\n\r]*)/im,
			/^([^\n\r]*konsult[^\n\r]*)/im,
			/^([^\n\r]*chef[^\n\r]*)/im,
			/^([^\n\r]*specialist[^\n\r]*)/im,
			/^([^\n\r]*analytiker[^\n\r]*)/im,
			/^([^\n\r]*projektledare[^\n\r]*)/im,

			// Role-specific patterns (English)
			/^([^\n\r]*developer[^\n\r]*)/im,
			/^([^\n\r]*engineer[^\n\r]*)/im,
			/^([^\n\r]*manager[^\n\r]*)/im,
			/^([^\n\r]*analyst[^\n\r]*)/im,
			/^([^\n\r]*consultant[^\n\r]*)/im
		];

		for (const pattern of titlePatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				let extractedTitle = match[1].trim();

				// Clean up the extracted title
				extractedTitle = extractedTitle
					.replace(/^[^\w\s]*/, '') // Remove leading non-word characters
					.replace(/[^\w\s]*$/, '') // Remove trailing non-word characters
					.replace(/\s+/g, ' ') // Normalize whitespace
					.replace(/^(vi\s+)?söker\s+(nu\s+)?(en\s+)?/i, '') // Remove "vi söker nu en" prefix
					.replace(/^(the\s+)?(a\s+)?/i, '') // Remove "the" or "a" prefix
					.trim();

				// Validate the extracted title
				if (extractedTitle.length >= 5 && extractedTitle.length <= 150 &&
					!/^(detaljer|referens|ort|plats|startdatum|slutdatum|omfattning|distansarbete)/i.test(extractedTitle) &&
					!/^(details|reference|location|start|end|scope|remote)/i.test(extractedTitle)) {
					title = `DocUploadTest: ${extractedTitle}`;
					break;
				}
			}
		}

		// Extract dates (English and Swedish)
		const datePatterns = [
			// English patterns
			/start date[:\s]+([^\n]+)/i,
			/starting[:\s]+([^\n]+)/i,
			/from[:\s]+([^\n]+)/i,
			/begins?[:\s]+([^\n]+)/i,

			// Swedish patterns
			/startdatum[:\s]+([^\n]+)/i,
			/start[:\s]+([^\n]+)/i,
			/börjar[:\s]+([^\n]+)/i,
			/från[:\s]+([^\n]+)/i,
			/påbörjas[:\s]+([^\n]+)/i
		];

		for (const pattern of datePatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				startDate = extractDate(match[1]);
				break;
			}
		}

		const endDatePatterns = [
			// English patterns
			/end date[:\s]+([^\n]+)/i,
			/until[:\s]+([^\n]+)/i,
			/to[:\s]+([^\n]+)/i,
			/ends?[:\s]+([^\n]+)/i,

			// Swedish patterns
			/slutdatum[:\s]+([^\n]+)/i,
			/slut[:\s]+([^\n]+)/i,
			/till[:\s]+([^\n]+)/i,
			/avslutas[:\s]+([^\n]+)/i,
			/varaktighet[:\s]+([^\n]+)/i
		];

		for (const pattern of endDatePatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				endDate = extractDate(match[1]);
				break;
			}
		}

		// Extract location (English and Swedish) - prioritize specific patterns
		const locationPatterns = [
			// High priority Swedish patterns (most specific first)
			/ort\s*:\s*([^\n\r]+)/i,
			/plats\s*:\s*([^\n\r]+)/i,
			/stad\s*:\s*([^\n\r]+)/i,
			/arbetsplats\s*:\s*([^\n\r]+)/i,
			/kontor\s*:\s*([^\n\r]+)/i,

			// High priority English patterns
			/location\s*:\s*([^\n\r]+)/i,
			/city\s*:\s*([^\n\r]+)/i,
			/office\s*:\s*([^\n\r]+)/i,

			// Lower priority patterns (more general)
			/based in[:\s]+([^\n\r]+)/i,
			/workplace[:\s]+([^\n\r]+)/i,
			/belägen[:\s]+([^\n\r]+)/i,
			/placering[:\s]+([^\n\r]+)/i,

			// Fallback patterns
			/plats[:\s]+([^\n\r]+)/i,
			/location[:\s]+([^\n\r]+)/i
		];

		for (const pattern of locationPatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				let extractedLocation = match[1].trim();

				// Clean up the extracted location
				extractedLocation = extractedLocation
					.replace(/[,;].*$/, '') // Remove everything after comma or semicolon
					.replace(/\s+/g, ' ') // Normalize whitespace
					.trim();

				// Only use if it looks like a valid location (not too long, no weird characters)
				if (extractedLocation.length > 0 && extractedLocation.length < 50 &&
					!/[{}[\]()]/g.test(extractedLocation)) {
					location = extractedLocation;
					break;
				}
			}
		}

		// Extract rate/salary information (English and Swedish)
		const ratePatterns = [
			// English patterns
			/rate[:\s]+([^\n]+)/i,
			/salary[:\s]+([^\n]+)/i,
			/compensation[:\s]+([^\n]+)/i,
			/hourly[:\s]+([^\n]+)/i,
			/pay[:\s]+([^\n]+)/i,
			/wage[:\s]+([^\n]+)/i,

			// Swedish patterns
			/arvode[:\s]+([^\n]+)/i,
			/lön[:\s]+([^\n]+)/i,
			/ersättning[:\s]+([^\n]+)/i,
			/timersättning[:\s]+([^\n]+)/i,
			/timlön[:\s]+([^\n]+)/i,
			/pris[:\s]+([^\n]+)/i,
			/kostnad[:\s]+([^\n]+)/i,

			// Currency patterns (works for both languages)
			/(\d+\s*SEK[^\n]*)/i,
			/(\d+\s*kr[^\n]*)/i,
			/(\d+\s*kronor[^\n]*)/i,
			/(\d+\s*EUR[^\n]*)/i,
			/(\d+\s*USD[^\n]*)/i,
			/(\d+\s*[^\n]*tim[^\n]*)/i, // "per timme" or "per hour"
			/(\d+\s*[^\n]*månad[^\n]*)/i // "per månad" or "per month"
		];

		for (const pattern of ratePatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				rate = match[1].trim();
				break;
			}
		}

		if (!description) {
			description = jobContent.trim();
		}
	}
	
	function extractDate(dateStr: string): string {
		// Enhanced date extraction for both English and Swedish formats
		let cleaned = dateStr.trim();

		// Handle Swedish month names
		const swedishMonths: { [key: string]: string } = {
			'januari': '01', 'jan': '01',
			'februari': '02', 'feb': '02',
			'mars': '03', 'mar': '03',
			'april': '04', 'apr': '04',
			'maj': '05',
			'juni': '06', 'jun': '06',
			'juli': '07', 'jul': '07',
			'augusti': '08', 'aug': '08',
			'september': '09', 'sep': '09',
			'oktober': '10', 'okt': '10',
			'november': '11', 'nov': '11',
			'december': '12', 'dec': '12'
		};

		// Replace Swedish month names with numbers
		for (const [swedish, number] of Object.entries(swedishMonths)) {
			const regex = new RegExp(swedish, 'gi');
			cleaned = cleaned.replace(regex, number);
		}

		// Handle various date formats
		// YYYY-MM-DD, DD/MM/YYYY, DD.MM.YYYY, DD-MM-YYYY
		const datePatterns = [
			/(\d{4})-(\d{1,2})-(\d{1,2})/,  // YYYY-MM-DD
			/(\d{1,2})[\/\.\-](\d{1,2})[\/\.\-](\d{4})/, // DD/MM/YYYY
			/(\d{1,2})\s+(\d{1,2})\s+(\d{4})/ // DD MM YYYY
		];

		for (const pattern of datePatterns) {
			const match = cleaned.match(pattern);
			if (match) {
				let year, month, day;

				if (pattern === datePatterns[0]) {
					// YYYY-MM-DD format
					[, year, month, day] = match;
				} else {
					// DD/MM/YYYY or DD MM YYYY format
					[, day, month, year] = match;
				}

				// Ensure two-digit month and day
				month = month.padStart(2, '0');
				day = day.padStart(2, '0');

				const dateString = `${year}-${month}-${day}`;
				const date = new Date(dateString);

				if (!isNaN(date.getTime())) {
					return dateString;
				}
			}
		}

		// Fallback: try to parse as-is
		const fallbackCleaned = cleaned.replace(/[^\d\/\-\.]/g, '');
		const date = new Date(fallbackCleaned);
		if (!isNaN(date.getTime())) {
			return date.toISOString().split('T')[0];
		}

		return '';
	}
	
	async function uploadToCinode() {
		if (!title || !selectedCustomerId) {
			alert('Please fill in all required fields');
			return;
		}
		
		isUploading = true;
		
		try {
			const jobData = {
				title,
				description,
				startDate,
				endDate,
				location,
				customerId: selectedCustomerId,
				rate,
				fileName
			};
			
			const response = await fetch('/api/cinode/upload', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(jobData)
			});
			
			if (response.ok) {
				await response.json();
				alert('Job successfully uploaded to Cinode!');
				sessionStorage.removeItem('jobContent');
				sessionStorage.removeItem('uploadedFileName');
				goto('/');
			} else {
				const error = await response.text();
				alert(`Upload failed: ${error}`);
			}
		} catch (error) {
			console.error('Upload error:', error);
			alert('Upload failed. Please try again.');
		} finally {
			isUploading = false;
		}
	}
	
	function goBack() {
		goto('/upload');
	}
</script>

<div class="container">
	<h1>Extract Job Information</h1>
	<p>Review and edit the extracted information before uploading to Cinode</p>
	
	{#if fileName}
		<div class="file-info">
			<p>📄 Source: {fileName}</p>
		</div>
	{/if}

	{#if extractionMethod}
		<div class="extraction-info">
			<p>🤖 Extraction Method: {extractionMethod}</p>
			{#if extractionConfidence > 0}
				<p>📊 Confidence: {Math.round(extractionConfidence * 100)}%</p>
			{/if}
		</div>
	{/if}
	
	{#if isExtracting}
		<div class="loading">
			<p>🔍 Extracting information...</p>
		</div>
	{:else}
		<form on:submit|preventDefault={uploadToCinode}>
			<div class="form-grid">
				<div class="form-group">
					<label for="title">Job Title *</label>
					<input
						id="title"
						type="text"
						bind:value={title}
						placeholder="e.g., Senior Frontend Developer"
						required
					/>
				</div>
				
				<div class="form-group">
					<label for="customer">Customer *</label>
					{#if isLoadingCustomers}
						<div class="loading-customers">
							<p>Loading customers...</p>
						</div>
					{:else if customerError}
						<div class="error-message">
							<p>Error: {customerError}</p>
							<button type="button" on:click={fetchCustomers} class="retry-btn">Retry</button>
						</div>
					{:else}
						<select
							id="customer"
							bind:value={selectedCustomerId}
							on:change={handleCustomerChange}
						>
							<option value={null}>Select a customer...</option>
							{#each customers as customer}
								<option value={customer.id}>{customer.name}</option>
							{/each}
						</select>
					{/if}
				</div>

				
				<div class="form-group">
					<label for="location">Location</label>
					<input
						id="location"
						type="text"
						bind:value={location}
						placeholder="e.g., Stockholm, Sweden"
					/>
				</div>

				<div class="form-group">
					<label for="rate">Rate</label>
					<input
						id="rate"
						type="text"
						bind:value={rate}
						placeholder="e.g., 800 SEK/hour"
					/>
				</div>
				
				<div class="form-group">
					<label for="startDate">Start Date</label>
					<input
						id="startDate"
						type="date"
						bind:value={startDate}
					/>
				</div>
				
				<div class="form-group">
					<label for="endDate">End Date</label>
					<input
						id="endDate"
						type="date"
						bind:value={endDate}
					/>
				</div>
				
				<div class="form-group full-width">
					<label for="description">Description</label>
					<textarea
						id="description"
						bind:value={description}
						placeholder="Job description and requirements"
						rows="6"
						required
					></textarea>
				</div>
				

				
			</div>
			
			<div class="actions">
				<button type="button" on:click={goBack} class="btn btn-secondary">
					← Back
				</button>
				<button type="submit" class="btn btn-primary" disabled={isUploading}>
					{isUploading ? 'Uploading...' : 'Upload to Cinode'}
				</button>
			</div>
		</form>
	{/if}
</div>

<style>
	.container {
		max-width: 900px;
		margin: 0 auto;
		padding: 20px;
	}
	
	h1 {
		color: #333;
		margin-bottom: 8px;
	}
	
	p {
		color: #666;
		margin-bottom: 30px;
	}
	
	.file-info {
		margin-bottom: 20px;
		padding: 12px;
		background: #e8f5e8;
		border-radius: 6px;
		border-left: 4px solid #22c55e;
	}

	.file-info p {
		margin: 0;
		color: #166534;
		font-weight: 500;
	}

	.extraction-info {
		margin-bottom: 20px;
		padding: 12px;
		background: #f0f9ff;
		border-radius: 6px;
		border-left: 4px solid #0ea5e9;
	}

	.extraction-info p {
		margin: 0;
		color: #0c4a6e;
		font-weight: 500;
		font-size: 14px;
	}

	.extraction-info p:not(:last-child) {
		margin-bottom: 4px;
	}
	
	.loading {
		text-align: center;
		padding: 40px;
		color: #666;
	}
	
	.form-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20px;
		margin-bottom: 30px;
	}
	
	.form-group {
		display: flex;
		flex-direction: column;
	}
	
	.form-group.full-width {
		grid-column: 1 / -1;
	}
	
	label {
		font-weight: 600;
		margin-bottom: 6px;
		color: #333;
	}
	
	input, select, textarea {
		padding: 10px 12px;
		border: 2px solid #e1e5e9;
		border-radius: 6px;
		font-family: inherit;
		font-size: 14px;
	}
	
	input:focus, select:focus, textarea:focus {
		outline: none;
		border-color: #007acc;
	}
	
	textarea {
		resize: vertical;
		min-height: 120px;
	}
	
	.actions {
		display: flex;
		gap: 12px;
		justify-content: space-between;
	}
	
	.btn {
		padding: 12px 24px;
		border: none;
		border-radius: 6px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s;
		font-size: 14px;
	}
	
	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}
	
	.btn-secondary {
		background: #f3f4f6;
		color: #374151;
	}
	
	.btn-secondary:hover:not(:disabled) {
		background: #e5e7eb;
	}
	
	.btn-primary {
		background: #007acc;
		color: white;
	}
	
	.btn-primary:hover:not(:disabled) {
		background: #005a9e;
	}

	.loading-customers {
		padding: 10px 12px;
		background: #f8f9fa;
		border: 2px solid #e1e5e9;
		border-radius: 6px;
		color: #666;
		font-style: italic;
	}

	.error-message {
		padding: 10px 12px;
		background: #fef2f2;
		border: 2px solid #fecaca;
		border-radius: 6px;
		color: #dc2626;
	}

	.retry-btn {
		margin-top: 8px;
		padding: 6px 12px;
		background: #dc2626;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-size: 12px;
	}

	.retry-btn:hover {
		background: #b91c1c;
	}

	@media (max-width: 768px) {
		.form-grid {
			grid-template-columns: 1fr;
		}
		
		.actions {
			flex-direction: column;
		}
		
		.btn {
			width: 100%;
		}
	}
</style>
