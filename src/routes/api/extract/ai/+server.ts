import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { createAIExtractionService } from '$lib/services/aiExtraction';
import { OPENAI_API_KEY } from '$env/static/private';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { content } = await request.json();

    if (!content || typeof content !== 'string') {
      return json(
        { success: false, error: 'Document content is required' },
        { status: 400 }
      );
    }

    if (!OPENAI_API_KEY) {
      return json(
        { 
          success: false, 
          error: 'OpenAI API key not configured. Please set OPENAI_API_KEY in your .env file.',
          fallbackToRegex: true
        },
        { status: 503 }
      );
    }

    // Create AI extraction service
    const aiService = createAIExtractionService(OPENAI_API_KEY);

    // Extract job data using AI
    const result = await aiService.extractJobData(content);

    if (!result.success) {
      return json(
        {
          success: false,
          error: result.error || 'AI extraction failed',
          fallbackToRegex: true
        },
        { status: 500 }
      );
    }

    // Add prefix to title for consistency with current system
    if (result.data?.title) {
      result.data.title = `DocUploadTest: ${result.data.title}`;
    }

    return json({
      success: true,
      data: result.data,
      confidence: result.confidence,
      extractionMethod: 'ai'
    });

  } catch (error) {
    console.error('AI extraction endpoint error:', error);
    
    return json(
      {
        success: false,
        error: 'Internal server error during AI extraction',
        details: error instanceof Error ? error.message : 'Unknown error',
        fallbackToRegex: true
      },
      { status: 500 }
    );
  }
};
