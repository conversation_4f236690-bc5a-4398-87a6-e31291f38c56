import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import { Connection, Client } from '@temporalio/client';
import { getEnvVar, getEnvVarAsNumber } from '@cinode-platform/shared';

// Import routes
import authRoutes from './routes/auth';
import documentRoutes from './routes/documents';
import projectRoutes from './routes/projects';
import templateRoutes from './routes/templates';
import workflowRoutes from './routes/workflows';
import healthRoutes from './routes/health';

// Import middleware
import { errorHandler } from './middleware/error-handler';
import { authMiddleware } from './middleware/auth';

// Load environment variables
dotenv.config();

const app = express();
const PORT = getEnvVarAsNumber('API_GATEWAY_PORT', 8000);

// Security middleware
app.use(helmet());
app.use(cors({
  origin: getEnvVar('CORS_ORIGINS', 'http://localhost:3000').split(','),
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: getEnvVarAsNumber('RATE_LIMIT_WINDOW_MS', 15 * 60 * 1000), // 15 minutes
  max: getEnvVarAsNumber('RATE_LIMIT_MAX_REQUESTS', 100), // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use(limiter);

// General middleware
app.use(compression());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Initialize Temporal client
let temporalClient: Client;

async function initializeTemporalClient() {
  try {
    const connection = await Connection.connect({
      address: getEnvVar('TEMPORAL_ADDRESS', 'localhost:7233'),
    });
    
    temporalClient = new Client({
      connection,
      namespace: getEnvVar('TEMPORAL_NAMESPACE', 'default'),
    });
    
    console.log('✅ Connected to Temporal server');
  } catch (error) {
    console.error('❌ Failed to connect to Temporal server:', error);
    process.exit(1);
  }
}

// Make Temporal client available to routes
app.use((req, res, next) => {
  req.temporalClient = temporalClient;
  next();
});

// Health check (no auth required)
app.use('/health', healthRoutes);

// Public routes (no auth required)
app.use('/api/auth', authRoutes);

// Protected routes (auth required)
app.use('/api/documents', authMiddleware, documentRoutes);
app.use('/api/projects', authMiddleware, projectRoutes);
app.use('/api/templates', authMiddleware, templateRoutes);
app.use('/api/workflows', authMiddleware, workflowRoutes);

// API documentation
app.get('/api', (req, res) => {
  res.json({
    name: 'Cinode Platform API Gateway',
    version: '1.0.0',
    description: 'API Gateway for Cinode Temporal Platform',
    endpoints: {
      auth: '/api/auth',
      documents: '/api/documents',
      projects: '/api/projects',
      templates: '/api/templates',
      workflows: '/api/workflows',
      health: '/health'
    },
    documentation: '/api/docs',
    temporal: {
      ui: 'http://localhost:8080',
      namespace: getEnvVar('TEMPORAL_NAMESPACE', 'default')
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    message: `Cannot ${req.method} ${req.originalUrl}`,
    timestamp: new Date()
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🔄 SIGTERM received, shutting down gracefully...');
  
  if (temporalClient) {
    await temporalClient.connection.close();
    console.log('✅ Temporal connection closed');
  }
  
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🔄 SIGINT received, shutting down gracefully...');
  
  if (temporalClient) {
    await temporalClient.connection.close();
    console.log('✅ Temporal connection closed');
  }
  
  process.exit(0);
});

// Start server
async function startServer() {
  try {
    await initializeTemporalClient();
    
    app.listen(PORT, () => {
      console.log(`🚀 API Gateway running on port ${PORT}`);
      console.log(`📚 API Documentation: http://localhost:${PORT}/api`);
      console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
      console.log(`⚡ Temporal UI: http://localhost:8080`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

// Extend Express Request type to include Temporal client
declare global {
  namespace Express {
    interface Request {
      temporalClient: Client;
      user?: {
        id: string;
        email: string;
        role: string;
      };
    }
  }
}
