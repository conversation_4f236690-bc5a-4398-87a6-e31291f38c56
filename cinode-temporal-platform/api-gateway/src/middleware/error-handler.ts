import { Request, Response, NextFunction } from 'express';
import { createErrorResponse, ErrorCode } from '@cinode-platform/shared';

export interface AppError extends Error {
  statusCode?: number;
  code?: ErrorCode;
  details?: Record<string, any>;
}

export function errorHandler(
  error: AppError,
  req: Request,
  res: Response,
  next: NextFunction
) {
  // Log error for debugging
  console.error('API Gateway Error:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    body: req.body,
    user: req.user?.id
  });

  // Default error response
  let statusCode = error.statusCode || 500;
  let errorCode = error.code || ErrorCode.INTERNAL_ERROR;
  let message = error.message || 'Internal server error';

  // Handle specific error types
  if (error.name === 'ValidationError') {
    statusCode = 400;
    errorCode = ErrorCode.VALIDATION_ERROR;
    message = 'Validation failed';
  }

  if (error.name === 'CastError') {
    statusCode = 400;
    errorCode = ErrorCode.VALIDATION_ERROR;
    message = 'Invalid ID format';
  }

  if (error.name === 'MongoError' && (error as any).code === 11000) {
    statusCode = 409;
    errorCode = ErrorCode.CONFLICT;
    message = 'Resource already exists';
  }

  // Handle Temporal workflow errors
  if (error.message.includes('workflow')) {
    errorCode = ErrorCode.WORKFLOW_ERROR;
  }

  // Handle external API errors
  if (error.message.includes('ECONNREFUSED') || error.message.includes('timeout')) {
    statusCode = 503;
    errorCode = ErrorCode.EXTERNAL_API_ERROR;
    message = 'External service unavailable';
  }

  // Don't expose internal errors in production
  if (process.env.NODE_ENV === 'production' && statusCode === 500) {
    message = 'Internal server error';
  }

  const errorResponse = createErrorResponse(message, errorCode);
  
  // Add error details in development
  if (process.env.NODE_ENV === 'development') {
    (errorResponse as any).details = {
      stack: error.stack,
      originalMessage: error.message,
      ...error.details
    };
  }

  res.status(statusCode).json(errorResponse);
}

export function asyncHandler(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

export function createAppError(
  message: string,
  statusCode: number = 500,
  code: ErrorCode = ErrorCode.INTERNAL_ERROR,
  details?: Record<string, any>
): AppError {
  const error = new Error(message) as AppError;
  error.statusCode = statusCode;
  error.code = code;
  error.details = details;
  return error;
}
