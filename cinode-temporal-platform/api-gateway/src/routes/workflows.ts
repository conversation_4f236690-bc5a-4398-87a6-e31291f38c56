import { Router } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ErrorCode,
  DocumentProcessingWorkflowInput,
  ProjectCreationWorkflowInput,
  TemplateEmailWorkflowInput
} from '@cinode-platform/shared';
import { asyncHandler, createAppError } from '../middleware/error-handler';

const router = Router();

// Start document processing workflow
router.post('/document-processing', asyncHandler(async (req, res) => {
  const { documentId, extractionMethod } = req.body;
  
  if (!documentId) {
    throw createAppError('Document ID is required', 400, ErrorCode.VALIDATION_ERROR);
  }

  const workflowInput: DocumentProcessingWorkflowInput = {
    documentId,
    userId: req.user!.id,
    extractionMethod: extractionMethod || 'auto',
    correlationId: uuidv4()
  };

  try {
    const handle = await req.temporalClient.workflow.start('documentProcessingWorkflow', {
      args: [workflowInput],
      taskQueue: 'cinode-tasks',
      workflowId: `document-processing-${documentId}-${Date.now()}`,
    });

    res.json(createSuccessResponse({
      workflowId: handle.workflowId,
      runId: handle.firstExecutionRunId,
      status: 'started'
    }, 'Document processing workflow started'));
  } catch (error) {
    throw createAppError(
      `Failed to start document processing workflow: ${error}`,
      500,
      ErrorCode.WORKFLOW_ERROR
    );
  }
}));

// Start project creation workflow
router.post('/project-creation', asyncHandler(async (req, res) => {
  const { extractedData, templateId, autoCreateRoles } = req.body;
  
  if (!extractedData) {
    throw createAppError('Extracted data is required', 400, ErrorCode.VALIDATION_ERROR);
  }

  const workflowInput: ProjectCreationWorkflowInput = {
    extractedData,
    userId: req.user!.id,
    templateId,
    autoCreateRoles: autoCreateRoles !== false, // default to true
    correlationId: uuidv4()
  };

  try {
    const handle = await req.temporalClient.workflow.start('projectCreationWorkflow', {
      args: [workflowInput],
      taskQueue: 'cinode-tasks',
      workflowId: `project-creation-${req.user!.id}-${Date.now()}`,
    });

    res.json(createSuccessResponse({
      workflowId: handle.workflowId,
      runId: handle.firstExecutionRunId,
      status: 'started'
    }, 'Project creation workflow started'));
  } catch (error) {
    throw createAppError(
      `Failed to start project creation workflow: ${error}`,
      500,
      ErrorCode.WORKFLOW_ERROR
    );
  }
}));

// Start template email workflow
router.post('/template-email', asyncHandler(async (req, res) => {
  const { templateId, variables, recipients } = req.body;
  
  if (!templateId || !variables || !recipients) {
    throw createAppError(
      'Template ID, variables, and recipients are required',
      400,
      ErrorCode.VALIDATION_ERROR
    );
  }

  const workflowInput: TemplateEmailWorkflowInput = {
    templateId,
    variables,
    recipients,
    userId: req.user!.id,
    correlationId: uuidv4()
  };

  try {
    const handle = await req.temporalClient.workflow.start('templateEmailWorkflow', {
      args: [workflowInput],
      taskQueue: 'cinode-tasks',
      workflowId: `template-email-${templateId}-${Date.now()}`,
    });

    res.json(createSuccessResponse({
      workflowId: handle.workflowId,
      runId: handle.firstExecutionRunId,
      status: 'started'
    }, 'Template email workflow started'));
  } catch (error) {
    throw createAppError(
      `Failed to start template email workflow: ${error}`,
      500,
      ErrorCode.WORKFLOW_ERROR
    );
  }
}));

// Get workflow status
router.get('/:workflowId/status', asyncHandler(async (req, res) => {
  const { workflowId } = req.params;

  try {
    const handle = req.temporalClient.workflow.getHandle(workflowId);
    const description = await handle.describe();
    
    res.json(createSuccessResponse({
      workflowId: description.workflowId,
      runId: description.runId,
      status: description.status,
      startTime: description.startTime,
      executionTime: description.executionTime,
      closeTime: description.closeTime,
      workflowType: description.workflowType
    }));
  } catch (error) {
    throw createAppError(
      `Failed to get workflow status: ${error}`,
      500,
      ErrorCode.WORKFLOW_ERROR
    );
  }
}));

// Get workflow result
router.get('/:workflowId/result', asyncHandler(async (req, res) => {
  const { workflowId } = req.params;

  try {
    const handle = req.temporalClient.workflow.getHandle(workflowId);
    const result = await handle.result();
    
    res.json(createSuccessResponse(result, 'Workflow result retrieved'));
  } catch (error) {
    throw createAppError(
      `Failed to get workflow result: ${error}`,
      500,
      ErrorCode.WORKFLOW_ERROR
    );
  }
}));

// Cancel workflow
router.post('/:workflowId/cancel', asyncHandler(async (req, res) => {
  const { workflowId } = req.params;
  const { reason } = req.body;

  try {
    const handle = req.temporalClient.workflow.getHandle(workflowId);
    await handle.cancel();
    
    res.json(createSuccessResponse({
      workflowId,
      status: 'cancelled',
      reason: reason || 'Cancelled by user'
    }, 'Workflow cancelled successfully'));
  } catch (error) {
    throw createAppError(
      `Failed to cancel workflow: ${error}`,
      500,
      ErrorCode.WORKFLOW_ERROR
    );
  }
}));

// Send signal to workflow
router.post('/:workflowId/signal', asyncHandler(async (req, res) => {
  const { workflowId } = req.params;
  const { signalName, args } = req.body;

  if (!signalName) {
    throw createAppError('Signal name is required', 400, ErrorCode.VALIDATION_ERROR);
  }

  try {
    const handle = req.temporalClient.workflow.getHandle(workflowId);
    await handle.signal(signalName, ...(args || []));
    
    res.json(createSuccessResponse({
      workflowId,
      signalName,
      status: 'sent'
    }, 'Signal sent successfully'));
  } catch (error) {
    throw createAppError(
      `Failed to send signal: ${error}`,
      500,
      ErrorCode.WORKFLOW_ERROR
    );
  }
}));

// Query workflow
router.get('/:workflowId/query/:queryName', asyncHandler(async (req, res) => {
  const { workflowId, queryName } = req.params;

  try {
    const handle = req.temporalClient.workflow.getHandle(workflowId);
    const result = await handle.query(queryName);
    
    res.json(createSuccessResponse({
      workflowId,
      queryName,
      result
    }, 'Query executed successfully'));
  } catch (error) {
    throw createAppError(
      `Failed to execute query: ${error}`,
      500,
      ErrorCode.WORKFLOW_ERROR
    );
  }
}));

// List workflows (with pagination)
router.get('/', asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, status, workflowType } = req.query;
  
  try {
    // Note: This is a simplified implementation
    // In production, you'd want to use Temporal's visibility APIs
    const workflows = await req.temporalClient.workflow.list({
      query: `WorkflowType="${workflowType || ''}" AND ExecutionStatus="${status || ''}"`,
    });

    const startIndex = (Number(page) - 1) * Number(limit);
    const endIndex = startIndex + Number(limit);
    const paginatedWorkflows = workflows.slice(startIndex, endIndex);

    res.json(createSuccessResponse({
      workflows: paginatedWorkflows,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: workflows.length,
        totalPages: Math.ceil(workflows.length / Number(limit))
      }
    }));
  } catch (error) {
    throw createAppError(
      `Failed to list workflows: ${error}`,
      500,
      ErrorCode.WORKFLOW_ERROR
    );
  }
}));

export default router;
