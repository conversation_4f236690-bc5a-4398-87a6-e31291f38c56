# Cinode Temporal Platform

A microservices platform for managing Cinode job assignments using Temporal.io workflows.

## Architecture

This platform consists of:

- **Frontend**: React application with TypeScript
- **API Gateway**: Express.js gateway with authentication
- **Microservices**: Independent services for different domains
- **Temporal Workflows**: Orchestrated business processes
- **Temporal UI**: Workflow monitoring and debugging

## Services

### Core Services
- **auth-service**: User authentication and authorization
- **document-service**: File processing and AI extraction
- **project-service**: Cinode project management
- **template-service**: Email template engine
- **notification-service**: Email and SMS notifications

### Workflows
- **document-processing**: Extract data from uploaded documents
- **project-creation**: Create projects and associated roles
- **template-email**: Generate and send templated emails
- **user-onboarding**: Handle new user registration

## Quick Start

### Prerequisites
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL
- Redis

### Development Setup

1. **Clone and install dependencies**:
```bash
git clone <repository>
cd cinode-temporal-platform
npm run install:all
```

2. **Start infrastructure**:
```bash
docker-compose up -d
```

3. **Start Temporal server**:
```bash
npm run temporal:dev
```

4. **Start all services**:
```bash
npm run dev
```

5. **Access applications**:
- Frontend: http://localhost:3000
- API Gateway: http://localhost:8000
- Temporal UI: http://localhost:8080

## Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/cinode_platform
REDIS_URL=redis://localhost:6379

# External APIs
CINODE_API_BASE=https://api.cinode.com
CINODE_APP_KEY=your_app_key
CINODE_COMPANY_ID=your_company_id
OPENAI_API_KEY=your_openai_key

# Authentication
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=your_email
SMTP_PASS=your_password

# Temporal
TEMPORAL_ADDRESS=localhost:7233
TEMPORAL_NAMESPACE=default
```

## Development

### Project Structure
```
cinode-temporal-platform/
├── frontend/                 # React frontend application
├── api-gateway/             # Express.js API gateway
├── services/                # Microservices
│   ├── auth-service/
│   ├── document-service/
│   ├── project-service/
│   ├── template-service/
│   └── notification-service/
├── workflows/               # Temporal workflows
├── shared/                  # Shared types and utilities
├── docker-compose.yml       # Development infrastructure
└── package.json            # Root package.json
```

### Available Scripts

- `npm run dev` - Start all services in development mode
- `npm run build` - Build all services for production
- `npm run test` - Run tests across all services
- `npm run temporal:dev` - Start Temporal development server
- `npm run temporal:ui` - Open Temporal UI
- `npm run install:all` - Install dependencies for all services

## Deployment

### Production Build
```bash
npm run build
docker-compose -f docker-compose.prod.yml up -d
```

### Environment-specific Configurations
- `docker-compose.yml` - Development
- `docker-compose.prod.yml` - Production
- `docker-compose.test.yml` - Testing

## Contributing

1. Create feature branch from `main`
2. Make changes in appropriate service directory
3. Add tests for new functionality
4. Update documentation
5. Submit pull request

## Monitoring

- **Temporal UI**: Workflow execution monitoring
- **Health Checks**: `/health` endpoint on each service
- **Logs**: Centralized logging with structured format
- **Metrics**: Prometheus metrics collection

## License

MIT License
