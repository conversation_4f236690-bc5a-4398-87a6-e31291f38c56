{"name": "cinode-temporal-platform", "version": "1.0.0", "description": "Microservices platform for Cinode job assignments with Temporal.io", "private": true, "workspaces": ["frontend", "api-gateway", "services/*", "workflows", "shared"], "scripts": {"install:all": "npm install && npm run install:workspaces", "install:workspaces": "npm install --workspaces", "dev": "concurrently \"npm run dev:infrastructure\" \"npm run dev:services\" \"npm run dev:frontend\"", "dev:infrastructure": "docker-compose up -d postgres redis", "dev:services": "concurrently \"npm run dev:gateway\" \"npm run dev:auth\" \"npm run dev:document\" \"npm run dev:project\" \"npm run dev:template\" \"npm run dev:notification\" \"npm run dev:workflows\"", "dev:gateway": "npm run dev --workspace=api-gateway", "dev:auth": "npm run dev --workspace=auth-service", "dev:document": "npm run dev --workspace=document-service", "dev:project": "npm run dev --workspace=project-service", "dev:template": "npm run dev --workspace=template-service", "dev:notification": "npm run dev --workspace=notification-service", "dev:workflows": "npm run dev --workspace=workflows", "dev:frontend": "npm run dev --workspace=frontend", "build": "npm run build --workspaces", "test": "npm run test --workspaces", "test:integration": "jest --config=jest.integration.config.js", "lint": "npm run lint --workspaces", "lint:fix": "npm run lint:fix --workspaces", "temporal:dev": "temporal server start-dev --ui-port 8080 --db-filename temporal.db", "temporal:ui": "open http://localhost:8080", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "clean": "npm run clean --workspaces && rm -rf node_modules", "reset": "npm run clean && npm run install:all"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "concurrently": "^8.2.2", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/cinode-temporal-platform.git"}, "keywords": ["temporal", "microservices", "cinode", "typescript", "react", "workflows"], "author": "Your Team", "license": "MIT"}