#!/bin/bash

# Cinode Temporal Platform Setup Script
set -e

echo "🚀 Setting up Cinode Temporal Platform..."

# Check if required tools are installed
check_dependency() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 is not installed. Please install it first."
        exit 1
    fi
}

echo "📋 Checking dependencies..."
check_dependency "node"
check_dependency "npm"
check_dependency "docker"
check_dependency "docker-compose"

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ All dependencies are installed"

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please update the .env file with your actual configuration values"
else
    echo "✅ .env file already exists"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm run install:all

# Start infrastructure services
echo "🐳 Starting infrastructure services..."
docker-compose up -d postgres redis minio mailhog

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check if services are running
echo "🔍 Checking service health..."
if docker-compose ps | grep -q "Up"; then
    echo "✅ Infrastructure services are running"
else
    echo "❌ Some services failed to start. Check docker-compose logs"
    exit 1
fi

# Build shared package
echo "🔨 Building shared package..."
npm run build --workspace=shared

# Start Temporal server in background
echo "⚡ Starting Temporal server..."
npm run temporal:dev &
TEMPORAL_PID=$!

# Wait for Temporal to be ready
echo "⏳ Waiting for Temporal server to be ready..."
sleep 15

# Check if Temporal is running
if curl -f http://localhost:8080 > /dev/null 2>&1; then
    echo "✅ Temporal server is running"
else
    echo "❌ Temporal server failed to start"
    kill $TEMPORAL_PID 2>/dev/null || true
    exit 1
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📚 Next steps:"
echo "1. Update your .env file with actual API keys and configuration"
echo "2. Run 'npm run dev' to start all services"
echo "3. Open http://localhost:3000 for the frontend"
echo "4. Open http://localhost:8080 for Temporal UI"
echo "5. Open http://localhost:8025 for Mailhog (email testing)"
echo ""
echo "🔧 Available commands:"
echo "- npm run dev          # Start all services"
echo "- npm run build        # Build all services"
echo "- npm run test         # Run tests"
echo "- npm run temporal:ui  # Open Temporal UI"
echo "- docker-compose logs  # View service logs"
echo ""

# Kill background Temporal process
kill $TEMPORAL_PID 2>/dev/null || true
