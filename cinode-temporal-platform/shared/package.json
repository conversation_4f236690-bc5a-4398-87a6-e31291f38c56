{"name": "@cinode-platform/shared", "version": "1.0.0", "description": "Shared types and utilities for Cinode Temporal Platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "test": "jest"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.0"}, "files": ["dist"]}