import { z } from 'zod';

// User Validation Schemas
export const userSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  firstName: z.string().min(1).max(50),
  lastName: z.string().min(1).max(50),
  role: z.enum(['admin', 'manager', 'user']),
  isActive: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date()
});

export const loginRequestSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8)
});

export const registerRequestSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8).regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
    'Password must contain at least 8 characters, 1 uppercase, 1 lowercase, and 1 number'
  ),
  firstName: z.string().min(1).max(50),
  lastName: z.string().min(1).max(50)
});

// Document Validation Schemas
export const extractedJobDataSchema = z.object({
  title: z.string().optional(),
  description: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  location: z.string().optional(),
  company: z.string().optional(),
  rate: z.string().optional(),
  skills: z.array(z.string()).optional(),
  requirements: z.array(z.string()).optional(),
  benefits: z.array(z.string()).optional()
});

export const documentProcessingResultSchema = z.object({
  success: z.boolean(),
  data: extractedJobDataSchema.optional(),
  confidence: z.number().min(0).max(1).optional(),
  extractionMethod: z.enum(['ai', 'regex']).optional(),
  error: z.string().optional()
});

// Project Validation Schemas
export const projectRoleSchema = z.object({
  id: z.string().uuid(),
  projectId: z.string().uuid(),
  cinodeRoleId: z.string().optional(),
  title: z.string().min(1).max(200),
  description: z.string(),
  skills: z.array(z.string()),
  requirements: z.array(z.string()),
  rate: z.string().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  status: z.enum(['open', 'filled', 'closed'])
});

export const projectSchema = z.object({
  id: z.string().uuid(),
  cinodeProjectId: z.string().optional(),
  title: z.string().min(1).max(200),
  description: z.string(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  location: z.string().optional(),
  company: z.string().optional(),
  rate: z.string().optional(),
  status: z.enum(['draft', 'active', 'completed', 'cancelled']),
  createdBy: z.string().uuid(),
  createdAt: z.date(),
  updatedAt: z.date(),
  roles: z.array(projectRoleSchema)
});

// Template Validation Schemas
export const templateVariableSchema = z.object({
  name: z.string().min(1),
  type: z.enum(['string', 'number', 'date', 'boolean']),
  required: z.boolean(),
  defaultValue: z.string().optional(),
  description: z.string().optional()
});

export const templateSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(100),
  description: z.string(),
  type: z.enum(['email', 'sms', 'document']),
  subject: z.string().min(1).max(200),
  content: z.string().min(1),
  variables: z.array(templateVariableSchema),
  isActive: z.boolean(),
  createdBy: z.string().uuid(),
  createdAt: z.date(),
  updatedAt: z.date()
});

export const templateRenderRequestSchema = z.object({
  templateId: z.string().uuid(),
  variables: z.record(z.any())
});

// Notification Validation Schemas
export const emailAttachmentSchema = z.object({
  filename: z.string().min(1),
  content: z.instanceof(Buffer),
  contentType: z.string()
});

export const emailNotificationSchema = z.object({
  to: z.array(z.string().email()).min(1),
  cc: z.array(z.string().email()).optional(),
  bcc: z.array(z.string().email()).optional(),
  subject: z.string().min(1).max(200),
  content: z.string().min(1),
  isHtml: z.boolean().optional(),
  attachments: z.array(emailAttachmentSchema).optional()
});

// Workflow Input Validation Schemas
export const workflowInputSchema = z.object({
  userId: z.string().uuid(),
  correlationId: z.string().optional(),
  metadata: z.record(z.any()).optional()
});

export const documentProcessingWorkflowInputSchema = workflowInputSchema.extend({
  documentId: z.string().uuid(),
  extractionMethod: z.enum(['ai', 'regex', 'auto']).optional()
});

export const projectCreationWorkflowInputSchema = workflowInputSchema.extend({
  extractedData: extractedJobDataSchema,
  templateId: z.string().uuid().optional(),
  autoCreateRoles: z.boolean().optional()
});

export const templateEmailWorkflowInputSchema = workflowInputSchema.extend({
  templateId: z.string().uuid(),
  variables: z.record(z.any()),
  recipients: z.array(z.string().email()).min(1)
});

// API Response Validation Schemas
export const apiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional(),
  timestamp: z.date()
});

export const paginationSchema = z.object({
  page: z.number().int().min(1),
  limit: z.number().int().min(1).max(100),
  total: z.number().int().min(0),
  totalPages: z.number().int().min(0)
});

export const paginatedResponseSchema = apiResponseSchema.extend({
  data: z.array(z.any()).optional(),
  pagination: paginationSchema
});

// Query Parameter Schemas
export const paginationQuerySchema = z.object({
  page: z.string().transform(val => parseInt(val, 10)).pipe(z.number().int().min(1)).optional().default('1'),
  limit: z.string().transform(val => parseInt(val, 10)).pipe(z.number().int().min(1).max(100)).optional().default('10'),
  sort: z.string().optional(),
  order: z.enum(['asc', 'desc']).optional().default('desc')
});

export const searchQuerySchema = z.object({
  q: z.string().min(1).optional(),
  status: z.string().optional(),
  type: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional()
}).merge(paginationQuerySchema);

// File Upload Validation
export const fileUploadSchema = z.object({
  filename: z.string().min(1),
  mimetype: z.string().min(1),
  size: z.number().int().min(1).max(10 * 1024 * 1024), // 10MB max
  buffer: z.instanceof(Buffer)
});

// Health Check Validation
export const healthCheckDependencySchema = z.object({
  name: z.string(),
  status: z.enum(['healthy', 'unhealthy']),
  responseTime: z.number().optional(),
  error: z.string().optional()
});

export const healthCheckSchema = z.object({
  status: z.enum(['healthy', 'unhealthy', 'degraded']),
  timestamp: z.date(),
  uptime: z.number(),
  version: z.string(),
  dependencies: z.array(healthCheckDependencySchema)
});

// Validation Helper Functions
export function validateSchema<T>(schema: z.ZodSchema<T>, data: unknown): T {
  const result = schema.safeParse(data);
  if (!result.success) {
    throw new Error(`Validation failed: ${result.error.message}`);
  }
  return result.data;
}

export function validatePartialSchema<T>(schema: z.ZodSchema<T>, data: unknown): Partial<T> {
  const partialSchema = schema.partial();
  const result = partialSchema.safeParse(data);
  if (!result.success) {
    throw new Error(`Validation failed: ${result.error.message}`);
  }
  return result.data;
}
